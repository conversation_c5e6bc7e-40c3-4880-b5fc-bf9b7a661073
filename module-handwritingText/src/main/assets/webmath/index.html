<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8" />
	<meta name="viewport"
		  content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
	<title>公式计算器</title>
	<script type="text/javascript" charset="utf-8" src="js/jquery.js"></script>
	<script type="text/javascript" src="js/tex-svg.js" id="MathJax-script" async></script>
	<script>
		MathJax = {
            loader: { load: ['output/svg'] },
            chtml: { displayAlign: 'left' },
            options: { enableMenu: false },
            tex: {
                inlineMath: [['$', '$'], ['\$$', '\$$']],
                packages: { '[+]': ['noerrors', 'noundefined', 'fix-unicode'] },
                noundefined: { color: '', background: '', size: '' },
                macros: {
                    oiint: "{\\bf ∯}", oiiint: "{\\bf ∰}",
                    textperthousand: "{\\bf ‰}", iddots: "{\\bf ⋰}",
                    ae: "{\\bf æ}", copyright: "{\\bf ©}",
                    llbracket: "{\\bf ⟦}", oe: "{\\bf œ}",
                    texteuro: "{\\bf €}", fint: "{\\bf ⨏}", rrbracket: "{\\bf ⟧}",
                    mapsfrom: "{\\bf ⟻}", textwon: "{\\bf ₩}", lightning: "{\\bf ↯}",
                    textregistered: "{\\bf ®}", parr: "{\\bf ⅋}", female: "{\\bf ♀}",
                    male: "{\\bf ♂}", leftmoon: "{\\bf ☾}", AE: "{\\bf Æ}",
                    mathsterling: "{\\bf £}", sun: "{\\bf ☉}", textcent: "{\\bf ₵}"
                }
            },
            startup: {
                ready() {
                    const { Configuration } = MathJax._.input.tex.Configuration;
                    const { MapHandler } = MathJax._.input.tex.MapHandler;
                    const NodeUtil = MathJax._.input.tex.NodeUtil.default;
                    const { getRange } = MathJax._.core.MmlTree.OperatorDictionary;
                    function Other(parser, char) {
                        const font = parser.stack.env['font'];
                        let def = font ? { mathvariant: parser.stack.env['font'] } : {};
                        const remap = (MapHandler.getMap('remap')).lookup(char);
                        const range = getRange(char);
                        const type = range?.[3] || 'mo';
                        let mo = parser.create('token', type, def, (remap ? remap.char : char));
                        range?.[4] && mo.attributes.set('mathvariant', range[4]);
                        if (type === 'mo') {
                            NodeUtil.setProperty(mo, 'fixStretchy', true);
                            parser.configuration.addNode('fixStretchy', mo);
                        }
                        parser.Push(mo);
                    }
                    Configuration.create('fix-unicode', { fallback: { character: Other } });
                    MathJax.startup.defaultReady();
                }
            }
        };
	</script>
	<style>
		html, body {
            margin: 0;
            padding: 0;
        }

        #content {
            display: flex;
            width: 100%;
            height: 100%;
            overflow: auto;
            justify-content: safe center;
            align-items: center;
        }

        /* 去掉MathJax自带显示公式的上下外边距 */
        .MathJax_Display,
        .MJXc-display,
        .MathJax_SVG_Display {
            margin-top: 0 !important;
            margin-bottom: 0 !important;
        }

        /* 让公式的SVG顶部对齐，去掉基线空隙 */
        .MathJax_SVG_Display svg {
            display: block !important;
            vertical-align: top !important;
        }

        /* 每个公式容器 */
        #content .formula-item {
            display: block;
            margin: 0 !important;
            padding: 0 !important;
            line-height: 1;
        }

        /* 相邻公式间距 0px */
        #content .formula-item + .formula-item {
            margin-top: 2px !important;
        }

		#content .formula-item mjx-container[jax="SVG"][display="true"] {
			display: block;
			text-align: center;
			margin: 0.2em 0;
		}
	</style>
</head>

<body>
<div id="content" ondblclick="doubleClick()"></div>
<script type="text/javascript" src="./js/web_api.js"></script>
<script>

    showLatex = function (latex, color, backgroundColor) {
        const content = document.getElementById('content');
        content.style.color = color;
        content.style.backgroundColor = backgroundColor;
        content.style.width = "100%";

        let matrixMatch = latex.match(/^\\begin\{matrix\}([\s\S]*)\\end\{matrix\}$/);

        if (matrixMatch) {
            let inner = matrixMatch[1].trim();
            let rows = inner.split(/\\\\/).filter(r => r.trim() !== "");

            if (rows.length === 1) {
                // 单行公式
                content.style.display = "flex";
                content.style.justifyContent = "center";
                content.style.alignItems = "center";
                content.style.textAlign = "center";
                content.innerHTML = `$$${rows[0]}$$`;

                MathJax.typesetClear();
                MathJax.typesetPromise([content]).then(() => {
                    fixSVG();
                });

            } else {
                // 多行公式
                content.style.display = "block";
                content.style.textAlign = "left";

                let html = "";
                rows.forEach(row => {
                    let fontSize;
                    if (row.length <= 32) {
                        fontSize = "20px";
                    } else {
                        fontSize = "16px";
                    }
                    html += `<div class="formula-item">
                                <span style="font-size:${fontSize}; display:inline-block;">
                                    $$${row}$$
                                </span>
                             </div>`;
                });
                content.innerHTML = html;

                MathJax.typesetClear();
                MathJax.typesetPromise([content]).then(() => {
                    fixSVG();
                });
            }

        } else {
            // 普通单公式
            content.style.display = "flex";
            content.style.justifyContent = "center";
            content.style.alignItems = "center";
            content.style.textAlign = "center";
            content.innerHTML = `$$${latex}$$`;

            MathJax.typesetClear();
            MathJax.typeset([content]).then(() => {
                fixSVG();
            });
        }
    }

    // 针对SVG的间距修正
    function fixSVG() {
        document.querySelectorAll('#content svg').forEach(svg => {
            svg.style.margin = '0';
            svg.style.display = 'block';
            svg.style.verticalAlign = 'top';
        });
        document.querySelectorAll('#content .formula-item').forEach(div => {
            div.style.lineHeight = '1';
        });
    }

    show = function (latex) {
        const content = document.getElementById('content');
        content.innerHTML = `$$${latex}$$`;
        MathJax.typeset([content]).then(() => {
            fixSVG();
        });
    }

    updateStyle = function (color, backgroundColor) {
        const content = document.getElementById('content');
        content.setAttribute('style', `color:${color};background-color:${backgroundColor}`);
    }

    function getRect() {
        let contentDiv = document.getElementById('content');
        Android.rectChangeClick(contentDiv.scrollWidth, contentDiv.scrollHeight);
    }
</script>
<script>

	function exportSVG(formula, color, scale, type) {
    try {
        if (!formula || typeof formula !== 'string') {
            throw new Error('公式内容无效');
        }

        const wrapper = MathJax.tex2svg(`\\large{${formula}}`, {
            em: 10,
            ex: 5,
            display: true
        });

        const svgElement = wrapper.querySelector('svg');
        if (!svgElement) {
            throw new Error('无法生成 SVG 元素');
        }

        alert(scale);

        svgElement.querySelectorAll('g, path').forEach(el => {
            el.setAttribute('fill', color);
            el.setAttribute('stroke', color);
        });

        if (!svgElement.hasAttribute('width') || !svgElement.hasAttribute('height')) {
            const bbox = svgElement.getBBox();
            svgElement.setAttribute('width', bbox.width + 'px');
            svgElement.setAttribute('height', bbox.height + 'px');
            svgElement.setAttribute('viewBox', `${bbox.x} ${bbox.y} ${bbox.width} ${bbox.height}`);
        }

        const originalWidth = parseFloat(svgElement.getAttribute('width')) || 0;
        const originalHeight = parseFloat(svgElement.getAttribute('height')) || 0;

        // 用 Image 读取像素宽高再算参考尺寸
        const image = new Image();
        image.src = 'data:image/svg+xml;base64,' + window.btoa(unescape(encodeURIComponent(svgElement.outerHTML)));
        image.onload = function () {
            const dpi = scale;
            const margin = 4;
            const totalMargin = 8;
            const w = Math.round(image.width * dpi);
            const h = Math.round(image.height * dpi);
            const m2 = Math.round(totalMargin * dpi);

            const referenceWidth = w + m2;
            const referenceHeight = h + m2;

            const svgString = svgElement.outerHTML;

            const result = {
                success: true,
                svg: svgString,
                base64: 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgString))),
                originalWidth: originalWidth,
                originalHeight: originalHeight,
                referenceWidth: referenceWidth,
                referenceHeight: referenceHeight,
                scale: scale,
                type: type
            };

            Android.svgCallback(JSON.stringify(result), type);
        };

    } catch (error) {
        console.error('SVG导出错误:', error);
        Android.svgCallback(JSON.stringify({
            success: false,
            error: error.message,
            formula: formula,
            type: type
        }), type);
    }
}


    // SVG优化函数
    function optimizeSVG(svgString) {

        svgString = svgString.replace(/xmlns:NS\d+=".*?"/g, '');

        svgString = svgString.replace(/\s*style="[^"]*"/g, '');

        svgString = svgString.replace(/<metadata>[\s\S]*?<\/metadata>/g, '');

        svgString = svgString.replace(/>\s+</g, '><');
        svgString = svgString.replace(/\s{2,}/g, ' ');

        return svgString;
    }



        function exportImage(formula, color, scale, type) {
            try {
                let wrapper = MathJax.tex2svg(`\\large{${formula}}`, { em: 10, ex: 5, display: true })
                let mjOut = wrapper.getElementsByTagName("svg")[0];
                const list = Array.from(mjOut.getElementsByTagName("g"));
                list.forEach(i => i.setAttribute("fill", `${color}`));
                var image = new Image();
                image.src = 'data:image/svg+xml;base64,' + window.btoa(unescape(encodeURIComponent(mjOut.outerHTML)));
                image.onload = function () {
                    let dpi = scale;
                    let margin = 3;
                    let totalMargin = 6;
                    let w = Math.round(image.width * dpi);
                    let h = Math.round(image.height * dpi);
                    let m2 = Math.round(totalMargin * dpi);
                    var canvas = document.createElement('canvas');
                    canvas.width = w + m2;
                    canvas.height = h + m2;
                    var context = canvas.getContext('2d');
                    context.scale(dpi, dpi);
                    context.drawImage(image, margin, margin);
                    Android.imageCallback(canvas.toDataURL('image/png'), type);
                }
            } catch (error) {
                console.log(error);
            }
        }
</script>
<script type="text/javascript">
	function doubleClick() {
        Android.doubleClick();
    }
</script>
</body>
</html>