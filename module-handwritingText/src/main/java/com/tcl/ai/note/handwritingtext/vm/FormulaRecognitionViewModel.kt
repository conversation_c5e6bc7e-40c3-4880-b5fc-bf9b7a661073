package com.tcl.ai.note.handwritingtext.vm

import android.content.Context
import android.graphics.Bitmap
import android.graphics.RectF
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.sunia.HTREngine.sdk.ERecognitionMode
import com.sunia.HTREngine.sdk.Engine
import com.sunia.HTREngine.sdk.Params
import com.sunia.HTREngine.sdk.RecognizeListener
import com.sunia.HTREngine.sdk.RecognizePath
import com.sunia.HTREngine.sdk.editor.Editor
import com.sunia.penengine.sdk.data.ICurve
import com.sunia.penengine.sdk.data.ListData
import com.sunia.penengine.sdk.operate.edit.RecoDataType
import com.sunia.singlepage.sdk.InkFunc
import com.sunia.viewlib.utils.FileUtil
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.handwritingtext.math.FormulaEventManager
import com.tcl.ai.note.handwritingtext.math.FormulaEventType
import com.tcl.ai.note.handwritingtext.math.FormulaLayoutSorter
import com.tcl.ai.note.handwritingtext.math.FormulaLayoutSorter.Formula
import com.tcl.ai.note.handwritingtext.math.FormulaLayoutSorter.LayoutResult
import com.tcl.ai.note.handwritingtext.math.web.LatexItem
import com.tcl.ai.note.handwritingtext.utils.MathConstant
import com.tcl.ai.note.handwritingtext.utils.ViewLibUtils
import com.tcl.ai.note.sunia.EngineManager
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import java.io.File
import java.lang.ref.WeakReference
import java.util.Locale


/**
 *  author : junze.liu
 *  date : 2025-08-19 13:56
 *  description : 公式识别
 *  1、初始化识别引擎，待Engine初始化成功后，初始化识别器editor
 *  （数学计算和公式识别共用一个识别引擎，但功能有自己的editor，一个Engine可以对应多个editor）
 *  2、依据框选的轨迹，批量传递给editor，获取识别结果，解析识别结果
 *  3、将识别结果转成String，通过Webview加载js网页的方式展示数学公式（android控件无法正常显示数学公式中的特殊字符）
 *  4、对识别后的数学公式按照以下规则进行排版
 *  5、点击替换时，将字符导出SVG的方式（直接导出图片会存在字符集的问题，如德语下，string转成bitmap会crash），再转成图片 替换原始轨迹 绘制在画布层
 *
 *  识别后的公式排版逻辑：
 *  每一组数学公式都有自己的外接矩形，识别完的数学公式如何排版，按照如下规则，实现排版：
 *  找到公式集合中最顶端的公式作为参照物（以A为参照物），1，如果 公式A 的外接矩形RectF和公式B的外接完全不相交，
 *  且A的外接矩形的底部在B的外接矩形的顶部，则认为是A在B的上面（纵向排列）
 *  2、如果 公式A 的外接矩形和公式B的外接完全不相交，且A的外接矩形的顶部在B的外接矩形的底部，则认为是A在B的下面（纵向排列）
 *  3、如果A和B的外接矩形相交，B的外接矩形的横向中轴线在A的外接矩形区域内，再则认为是A和B是并排显示（横向排列），
 *  B中轴线不在A的外接区域内，则纵向排列，B的中轴线在A的上方，则认为B在A上，否则，则认为B在A下。当A和B的位置确定后，
 *  则以A和B组成的外接矩形区域的并集作为新的参照物，跟C做排版，依次类推，最终实现排版及排序
 *
 * 以最顶部的公式作为第一个参照物
 * 实现纵向排列判断（上下关系）
 * 实现横向排列判断（左右关系）
 * 使用StringBuilder按行拼接内容
 * 同一行内左侧内容先拼接
 * 左右位置判断
 *
 */
class FormulaRecognitionViewModel : ViewModel() {

    private var inkFuncWeakRef: WeakReference<InkFunc>? = null

    val recognitionState = mutableStateOf<RecognitionState>(RecognitionState.Idle)

    // 是否显示Loading（识别耗时超过600ms则显示loading）
    private val _showLoading = MutableStateFlow(false)
    var showLoading = _showLoading.asStateFlow()

    private var loadingJob: Job? = null

    // 排序管理类
    val sorter = FormulaLayoutSorter()

    fun resetState() {
        recognitionState.value = RecognitionState.Idle
    }

    sealed class RecognitionState {
        object Idle : RecognitionState()
        object Loading : RecognitionState()
        data class Success(
            val data: String,
            val result: List<LayoutResult>,
            val latexItemList: List<LatexItem>,
            val scale: Float
        ) : RecognitionState()
        data class Error(val message: String) : RecognitionState()
    }


    // 用于触发导出图片的事件
    private val _exportImageEvent = MutableSharedFlow<Unit>()
    val exportImageEvent = _exportImageEvent.asSharedFlow()


    // 触发导出图片
    suspend fun triggerExportImage() {
        _exportImageEvent.emit(Unit)
    }

    // 设置生成的Bitmap
    fun setBitmap(bitmap: Bitmap?) {
        replaceMathBitmapData(bitmap,mScale)
    }


    init {
        Logger.d(TAG,"------init------")
        initEngine()
        observeFormulaRecognitionEvents()
    }

    private fun initEngine() {
        viewModelScope.launch {
            EngineManager.engineState
                .filterNotNull()
                .collect { engine ->
                    Logger.d(TAG, "initEngine, 公式识别 collect, engine initialization successfully: $engine")
                    initEditor(engine)
                }
        }
    }


    private fun observeFormulaRecognitionEvents() {
        viewModelScope.launch {
            FormulaEventManager.formulaRecognitionEvents.collect { event ->
                when (event.type) {
                    FormulaEventType.FORMULA -> {
                        doMathRecognize(event.scale,event.offset,event.data,event.inkFunc)
                        Logger.d(TAG, "observeFormulaRecognitionEvents, formula recognition")
                    }

                    FormulaEventType.REPLACE -> {}
                }
            }
        }
    }


    /**
     * 执行数学公式识别
     * @param scale 画布缩放比例
     * @param offset 偏移量
     * @param data 轨迹数据
     * @param inkFunc 手绘引擎
     */
    private fun doMathRecognize(scale: Float, offset: FloatArray?, data: List<ICurve>?, inkFunc: InkFunc?) {
        Logger.v(TAG, "doMathRecognize, scale:$scale, offset:$offset, data:$data")

        _showLoading.value = false
        loadingJob?.cancel()

        viewModelScope.launchIO {
            // 仅当inkFunc发生变化时更新弱引用
            if (inkFuncWeakRef?.get() != inkFunc) {
                inkFuncWeakRef = WeakReference(inkFunc)
            }

            mScale = scale
            recognitionState.value = RecognitionState.Loading

            // 轨迹识别耗时超过600ms，则显示loading
            loadingJob = viewModelScope.launch {
                delay(600)
                if (recognitionState.value is RecognitionState.Loading) {
                    _showLoading.value = true
                }
            }

            // 批量传递数据
            val pathResult = syncRecognizePath(ViewLibUtils.engineCurveToRecognizePath(data, scale, offset))
            Logger.v(TAG, "doMathRecognize, pathResult:$pathResult")

            if (pathResult.isEmpty()) {
                recognitionState.value = RecognitionState.Error("")
                return@launchIO
            }

            try {
                // 解析识别结果
                val pathData = ViewLibUtils.parseRecoBaseDatas(pathResult, RectF())
                Logger.v(TAG, "doMathRecognize, pathData:$pathData")

                if (pathData == null) {
                    recognitionState.value = RecognitionState.Error("")
                    return@launchIO
                }

                // 按左上角顶点位置排序
                pathData.sortWith(compareBy { it.groupBoxRectF.top })

                val formulas = mutableListOf<Formula>()
                val txts = mutableListOf<String>()

                for (item in pathData) {
                    Logger.d(TAG, "doMathRecognize pathResult data ${item.dataType}")

                    if (item.dataType == RecoDataType.Formula_Math.value) {
                        Logger.d(TAG, "doMathRecognize text: ${item.groupText?.text} result: ${item.resultText?.text}, " +
                                "boxRectF:${item.groupBoxRectF}")

                        item.groupText?.text?.let { text ->
                            formulas.add(Formula(text, item.groupBoxRectF))
                            txts.add(text)
                        }
                    }
                }

                if (txts.isEmpty()) {
                    recognitionState.value = RecognitionState.Error("")
                    return@launchIO
                }

                // 对识别后的公式进行排序
                val (sortedResults, contentString, latexItemList) = sorter.sortFormulas(formulas)

                Logger.d(TAG, "doMathRecognize, formulas:$formulas, sortedResults:$sortedResults")
                Logger.d(TAG, "doMathRecognize, sort content, contentString:$contentString")

                recognitionState.value = RecognitionState.Success(contentString, sortedResults, latexItemList, mScale)

            } catch (e: Exception) {
                Logger.e(TAG, "doMathRecognize error:$e")
                recognitionState.value = RecognitionState.Error("")
            } finally {
                // 无论成功或失败，都取消延迟任务并重置showLoading
                loadingJob?.cancel()
                _showLoading.value = false
            }
        }
    }

    /**
     * 批量传递数据
     * @param recognizePaths 需要识别的路径列表
     * @return 识别结果
     */
    private fun syncRecognizePath(recognizePaths: MutableList<RecognizePath>?): String {
        Logger.d(TAG, "syncRecognizePath: ${recognizePaths?.size ?: "null"}")

        if (recognizePaths.isNullOrEmpty()) {
            return ""
        }

        return try {
            editor?.clearContent()

            // 批量传递数据
            recognizePaths.forEach { path ->
                editor?.addStrokePoints(path.points)?.let { strokeId ->
                    path.setStrokeId(strokeId.toLong())
                }
            }

            // 执行识别并获取结果
            editor?.doPageRecognize(true)
            val result = editor?.content ?: ""

            // 主动清理识别器内容，避免产生脏数据
            editor?.clearContent()

            Logger.d(TAG, "syncRecognizePath result: $result")
            result
        } catch (e: Exception) {
            Logger.e(TAG, "syncRecognizePath error: ${e.message}")
            ""
        }
    }



    private fun replaceMathBitmapData(bitmap: Bitmap?, ratio: Float) {
        Logger.e(TAG, "replaceMathBitmapData, ratio:$ratio")
        if (bitmap == null){
            Logger.e(TAG, "replaceMathBitmapData, bitmap is null")
            return
        }
        val inkFunc = inkFuncWeakRef?.get()
        val selectRect = inkFunc?.selectEditFunc?.selectRect
        if (selectRect == null) {
            Logger.d(TAG, "replaceMathBitmapData selectRect：$selectRect ")
            return
        }
        val recoImagePath = "${GlobalContext.instance.externalCacheDir}${File.separator}reco_image"
        val imgFile = File(recoImagePath, "reco_img_${System.currentTimeMillis()}.png")
        if (File(recoImagePath).exists()) {
            val del = File(recoImagePath).deleteRecursively()
            Logger.d(TAG,"replaceMathBitmapData, del:$del")
        }
        FileUtil.saveBitmapToFile(bitmap, imgFile)

        val newData = ListData()
        newData.bitmapData = arrayListOf()
        val bimapData = ListData.BitmapData()
        bimapData.path = imgFile.path

        // 计算bitmap在选择框内的尺寸
        val bitmapWidth = bitmap.width.toFloat() / ratio
        val bitmapHeight = bitmap.height.toFloat() / ratio

        // 计算选择框的尺寸
        val selectWidth = selectRect.width()
        val selectHeight = selectRect.height()

        // 计算垂直居中的偏移量
        val verticalOffset = (selectHeight - bitmapHeight) / 2.0f
        val horizontalOffset = 0f // 水平方向保持左对齐

        // 确保偏移量不为负数（避免超出选择框）
        val finalVerticalOffset = Math.max(0f, verticalOffset)
        val finalHorizontalOffset = Math.max(0f, horizontalOffset)

        bimapData.rectF = RectF(0f, 0f, bitmapWidth, bitmapHeight)
        val scaleInfo = inkFunc.scaleInfo
        bimapData.rectF.offset(
            (selectRect.left - scaleInfo.offsetX) / ratio + finalHorizontalOffset,
            (selectRect.top - scaleInfo.offsetY) / ratio + finalVerticalOffset
        )
        Logger.d(TAG, "replaceMathBitmapData selectRect：$selectRect ${bitmap.width}, ${bitmap.height}")
        Logger.d(TAG, "replaceMathBitmapData bitmap size: ${bitmapWidth}x${bitmapHeight}, select size: ${selectWidth}x${selectHeight}")
        Logger.d(TAG, "replaceMathBitmapData vertical offset: $finalVerticalOffset, horizontal offset: $finalHorizontalOffset")
        Logger.d(TAG, "replaceMathBitmapData final bimapData.rectF: ${bimapData.rectF}")
        newData.bitmapData.add(bimapData)
        inkFunc.selectEditFunc?.replaceData(newData, getUnSupportData(inkFunc), true)
    }

    private fun getUnSupportData(inkFunc: InkFunc?): MutableList<ICurve> {
        val unSupportData: MutableList<ICurve> = mutableListOf()
        inkFunc?.selectEditFunc?.selectCurveData?.let {
            for (curve in it) {
                if (curve.shapeId > 0) {
                    unSupportData.add(curve)
                }
            }
        }
        Logger.d(TAG, "getUnSupportData： ${unSupportData.size}")
        return unSupportData
    }


    override fun onCleared() {
        super.onCleared()
        inkFuncWeakRef = null
    }


    companion object {

        private val TAG = "FormulaRecognitionViewModel"

        private var editor: Editor? = null

        private var mScale: Float = 1.0f


        private fun initEditor(engine: Engine?) {
            Logger.d(TAG, "initEngine, engine:$engine")
            if (engine == null || (!engine.isValidate)) {
                Logger.w(TAG, "initEditor, engine is null or isValidate, return")
                return
            }
            val start = System.currentTimeMillis()
            Logger.d(TAG, " initEditor start")

            if (editor == null) {
                val params = getParams()
                editor = engine.createEditor(params)
                Logger.d(TAG, "initEditor end, editor:$editor, use time:${System.currentTimeMillis() - start}, params:$params")
                editor?.setRecognizeEngineListener(object : RecognizeListener {
                    override fun onLoaded(p0: Editor?) {
                        Logger.d(TAG, "editor onLoaded,  cost time:" + (System.currentTimeMillis() - start))
                    }

                    override fun onError(p0: Editor?, p1: Int, p2: Exception?) {
                        Logger.e(TAG, " editor onError, p1:$p1, p2:$p2")
                    }

                    override fun onContentChanged(p0: Editor, result: String?) {
                        Logger.d(TAG, "editor onContentChanged, result: $result")
                    }

                    override fun onAssociationalChanged(p0: Editor?, p1: String?) {
                        Logger.d(TAG, "editor onAssociationalChanged, p1: $p1")
                    }

                    override fun onCandidateChanged(p0: Editor?, p1: String?) {
                        Logger.d(TAG, "editor onCandidateChanged, p1: $p1")
                    }
                })

                editor?.open(params)
            }
            Logger.d(TAG, "initEditor, editor:$editor")
        }


        private fun getParams(): Params {
            val params: Params = Params.createInstance()
            params.mode = ERecognitionMode.MODE_ECR.value
            params.setDataDir("mixture/conf/mixture_zh")
            params.setConfigName("mixture-zh_CN.conf")
            params.setResultCoordinate(true)
            params.setWordSplitTimeLot(500)
            params.setResultAssociational(true)
            params.setResultCandidate(true)
            params.setResultPartitionCoordinate(true)
            params.setResultSpanProcessed(true)
            params.setResultCalculate(true)
            params.setKeyValuesConfig(buildParamsStr(GlobalContext.instance))
            Logger.d(TAG, "getParams, params:$params")
            return params
        }

        private fun buildParamsStr(context: Context): String {
            val builder = (MathConstant.NGRAM_PARAMS_STR +
                    MathConstant.POST_PARAMS_STR +
                    MathConstant.MERGE_PARAMS_STR +
                    MathConstant.ENABLE_MATH_COVERING +
                    MathConstant.ENABLE_DOT_CONVERT_MULTIPLICATION +
                    MathConstant.ENABLE_RESULT_CORRECT_RATE +
                    MathConstant.MIXTURE_MATH_CANDIATES_CORRENCT_RATE +
                    MathConstant.ENABLE_MIXTURE_BEATUTYCHARS +
                    MathConstant.ENABLE_MIXTURE_ERRORCORRECT +
                    MathConstant.ENABLE_MIXTURE_CANDIATES_REPLACE +
                    MathConstant.ENABLE_MIXTURE_VERTICAL_RESULT_CALCULATE +  //                Constant.ENABLE_MIXTURE_NTI +
                    MathConstant.ENABLE_MIXTURE_USE_EQUIDISTANT_NAI +
                    MathConstant.ENABLE_MIXTURE_USE_BEATUTY_CORRECTRATE +
                    MathConstant.MIXTURE_BEATUTY_CORRENCTRATE +
                    MathConstant.MIXTURE_BEATUTY_LENGTH +
                    MathConstant.MIXTURE_NBI_PARAMS +
                    MathConstant.MIX_NBI_FONTNAME).toString() +
                    java.lang.String.format(
                        Locale.getDefault(),
                        "dpi=%f;",
                        context.resources.displayMetrics.xdpi
                    )
            return builder.trim { it <= ' ' }
        }

    }
}