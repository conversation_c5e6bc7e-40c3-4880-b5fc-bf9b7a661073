package com.tcl.ai.note.handwritingtext.utils;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.PorterDuff;
import android.util.Log;
import com.caverock.androidsvg.SVG;
import com.caverock.androidsvg.SVGParseException;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UncheckedIOException;
import java.nio.charset.StandardCharsets;



/**
 * author : junze.liu
 * date : 2025-08-29 14:34
 * description :
 */

public class SvgToBitmapConverter {

    private static final String TAG = "SvgToBitmapConverter";

    /**
     * 将SVG字符串转换为Bitmap
     *
     * @param svgString SVG字符串内容
     * @param scale 缩放比例 (1.0 = 原始大小)
     * @param backgroundColor 背景颜色 (默认透明)
     * @return 转换后的Bitmap或null
     */
    public static Bitmap convertSvgToBitmap(String svgString, float scale, int backgroundColor,int width, int height) {
        try {

            SVG svg = parseSvg(svgString);
            if (svg == null) {
                return null;
            }

            Bitmap bitmap = createBitmap(width, height);

            renderToCanvas(svg, bitmap, scale, backgroundColor);

            return bitmap;
        } catch (Exception e) {
            Log.e(TAG, "SVG conversion failed: " + e.getMessage(), e);
            return null;
        }
    }


    private static SVG parseSvg(String svgString) throws RuntimeException {
        try (InputStream inputStream = new ByteArrayInputStream(svgString.getBytes(StandardCharsets.UTF_8))) {
            return SVG.getFromInputStream(inputStream);
        } catch (IOException e) {
            // 由于 ByteArrayInputStream 不会抛出 IOException，所以这里理论上不会执行
            throw new UncheckedIOException("Unexpected error", e);
        } catch (SVGParseException e) {
            throw new RuntimeException("Failed to parse SVG", e);
        }
    }




    private static Bitmap createBitmap(int width, int height) {
        // 使用ARGB_8888配置确保透明度支持
        return Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
    }

    private static void renderToCanvas(SVG svg, Bitmap bitmap, float scale, int backgroundColor) {
        Canvas canvas = new Canvas(bitmap);

        canvas.drawColor(backgroundColor, PorterDuff.Mode.CLEAR);

        // 获取SVG的文档尺寸
        float svgWidth = svg.getDocumentWidth();
        float svgHeight = svg.getDocumentHeight();

        // 如果SVG没有明确的尺寸，使用viewBox
        if (svgWidth <= 0 || svgHeight <= 0) {
            if (svg.getDocumentViewBox() != null) {
                svgWidth = svg.getDocumentViewBox().width();
                svgHeight = svg.getDocumentViewBox().height();
            }
        }

        // 计算缩放后的SVG尺寸
        float scaledSvgWidth = svgWidth * scale;
        float scaledSvgHeight = svgHeight * scale;

        // 计算垂直居中的偏移量
        float offsetX = 0; // 水平方向保持左对齐
        float offsetY = (bitmap.getHeight() - scaledSvgHeight) / 2.0f;

        // 确保偏移量不为负数（避免内容被裁剪）
        offsetY = Math.max(0, offsetY);

        Log.d(TAG, "renderToCanvas - bitmap size: " + bitmap.getWidth() + "x" + bitmap.getHeight() +
              ", svg size: " + svgWidth + "x" + svgHeight +
              ", scaled svg size: " + scaledSvgWidth + "x" + scaledSvgHeight +
              ", offset: (" + offsetX + ", " + offsetY + ")");

        // 应用缩放和偏移
        canvas.scale(scale, scale);
        canvas.translate(offsetX / scale, offsetY / scale);

        svg.renderToCanvas(canvas);
    }


}

