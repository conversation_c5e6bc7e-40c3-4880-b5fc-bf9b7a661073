package com.tcl.ai.note.inspiration.ui

import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.BaselineShift
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.viewmodel.compose.viewModel
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.database.entity.Album
import com.tcl.ai.note.inspiration.viewmodel.AddPageViewModel
import com.tcl.ai.note.inspiration.viewmodel.AlbumAnalysisState
import com.tcl.ai.note.inspiration.viewmodel.AlbumType
import com.tcl.ai.note.inspiration.viewmodel.type
import com.tcl.ai.note.journalbase.launchAppDetailsScreen
import com.tcl.ai.note.resources.R
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isFastClick
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.widget.components.checkLoginAndExecute
import com.tct.theme.core.designsystem.component.TclLinearProgressIndicator
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

@Composable
fun MomentScreen(
    loginHandler: (action: () -> Unit) -> Unit,
    modifier: Modifier = Modifier,
    viewModel: AddPageViewModel = hiltViewModel(),
    onAlbumClick: (List<String>) -> Unit,
    enableClick: () -> Unit
) {
    val uiState = viewModel.uiState.collectAsState().value
    val coroutineScope = rememberCoroutineScope()
    var loginCheckJob: Job? = null
    val lifecycleOwner = LocalLifecycleOwner.current
    val imagePickHelper = rememberPickImageHelper { uris ->
        viewModel.handleGalleryUris(uris)
        onAlbumClick(viewModel.selectedImages.map { it.path })
    }

    DisposableEffect(lifecycleOwner) {
        // 生命周期回调
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                viewModel.initData()
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
    LaunchedEffect(uiState.analysisState) {
        if (uiState.analysisState is AlbumAnalysisState.NotStart) {
            return@LaunchedEffect
        }
        val progress = if (uiState.analysisState is AlbumAnalysisState.Analyzing) {
            (uiState.analysisState as AlbumAnalysisState.Analyzing).progress
        } else {
            0
        }
        AppDataStore.putData(DataStoreParam.KEY_ANALYSIS_PROGRESS, progress)
    }

    val scrollState = rememberScrollState()
    DisposableEffect(Unit) {
        onDispose {
            if (loginCheckJob?.isActive == true) {
                loginCheckJob!!.cancel()
            }
        }
    }
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
    ) {
        if (!uiState.permissionGranted) {
            PermissionDeniedView()
            return@Column
        }
        GalleryBtn(
            modifier = Modifier
                .padding(top = 24.dp, start = 12.dp, end = 12.dp)
                .fillMaxWidth()
                .height(44.dp)
        ) {
            if (isFastClick()){
                return@GalleryBtn
            }
            loginCheckJob = coroutineScope.launch {
                checkLoginAndExecute(loginHandler) {
                    imagePickHelper.invoke { }
                }
            }
        }
        if (!uiState.enabled) {
            NotEnabledView {
                enableClick()
            }
            return@Column
        }
        if (!uiState.existImage) {
            SystemAlbumEmptyView()
            return@Column
        }
        if (uiState.analysisState is AlbumAnalysisState.Failed && viewModel.albumMap.isEmpty()) {
            AlbumAnalysisFailedView {
                viewModel.analysisAlbums(isFailedRetry = true)
            }
            return@Column
        }

        Spacer(modifier = Modifier.height(24.dp))

        InspirationSuggestionPanel(uiState.analysisState)
        InspirationAlbumList { inspirationAlbum ->
            loginCheckJob = coroutineScope.launch {
                if (AccountController.getLoginState()) {
                    viewModel.selectImages(inspirationAlbum)
                    onAlbumClick(viewModel.selectedImages.map { it.path })
                } else {
                    loginHandler.invoke {
                        viewModel.selectImages(inspirationAlbum)
                        onAlbumClick(viewModel.selectedImages.map { it.path })
                    }
                }
            }
        }
    }
}

@Composable
fun GalleryBtn(
    modifier: Modifier = Modifier,
    darkTheme: Boolean = isSystemInDarkTheme(),
    galleryBtnClick: () -> Unit
) {
    Row(
        modifier = modifier
            .background(
                color = darkTheme.judge(
                    Color.White.copy(alpha = 0.08f),
                    Color.White.copy(alpha = 0.8f),
                ),
                RoundedCornerShape(22.dp)
            )
            .clip(RoundedCornerShape(22.dp))
            .clickable {
                galleryBtnClick()
            },
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            painter = painterResource(R.drawable.ic_picture),
            contentDescription = null,
            modifier = Modifier.size(24.dp),
            tint = com.tct.theme.core.designsystem.theme.TclTheme.colorScheme.tctStanderTextPrimary
        )

        Text(
            modifier = Modifier.padding(start = 8.dp),
            text = stringResource(R.string.gallery),
            textAlign = TextAlign.Center,
            color = com.tct.theme.core.designsystem.theme.TclTheme.colorScheme.tctStanderTextPrimary,
            fontSize = 16.sp,
            lineHeight = 24.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
private fun InspirationSuggestionPanel(analysisState: AlbumAnalysisState) {
    val viewModel = viewModel<AddPageViewModel>()
    Column(
        modifier = Modifier
            .padding(start = 24.dp, end = 24.dp)
    ) {
        when (analysisState) {
            is AlbumAnalysisState.Failed -> {
                AlbumAnalysisFailedPanel(
                    (analysisState.progress / 100f).coerceAtLeast(0f).coerceAtMost(1f)
                ) {
                    viewModel.analysisAlbums(isFailedRetry = true)
                }

                Spacer(modifier = Modifier.height(12.dp))
            }

            is AlbumAnalysisState.Analyzing -> {
                AlbumAnalyzingPanel(
                    (analysisState.progress / 100f).coerceAtLeast(0f).coerceAtMost(1f)
                )

                Spacer(modifier = Modifier.height(12.dp))
            }

            else -> {}
        }
    }
}

@Composable
private fun InspirationAlbumList(onAlbumClick: (inspirationAlbum: Album) -> Unit) {
    val viewModel = viewModel<AddPageViewModel>()
    val albumsList = viewModel.albumMap.keys.sortedByDescending { it.timestamp }.toList()
    repeat(albumsList.size) { index ->
        val inspirationAlbum = albumsList[index]
        val type = inspirationAlbum.type()
        val modifier = Modifier.clickable {
            onAlbumClick.invoke(inspirationAlbum)
        }
        when (type) {
            AlbumType.Single -> {
                SingleInspirationAlbumCard(inspirationAlbum, modifier)
            }

            AlbumType.Two -> {
                TwoInspirationAlbumsCard(inspirationAlbum, modifier)
            }

            AlbumType.Three -> {
                ThreeInspirationAlbumsCard(inspirationAlbum, modifier)
            }

            AlbumType.Four -> {
                FourInspirationAlbumsCard(inspirationAlbum, modifier)
            }

            AlbumType.Five -> {
                FiveInspirationAlbumsCard(inspirationAlbum, modifier)
            }

            AlbumType.More -> {
                MoreInspirationAlbumsCard(inspirationAlbum, modifier)
            }

            AlbumType.Empty -> {
            }
        }
    }
}

@Composable
private fun AlbumAnalyzingPanel(
    progress: Float,
    darkTheme: Boolean = isSystemInDarkTheme(),
) {
    TclLinearProgressIndicator(
        modifier = Modifier
            .padding(vertical = 9.dp)
            .height(6.dp)
            .fillMaxWidth(),
        progress = { progress }
    )
    Text(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 4.dp),
        text = buildAnnotatedString {
            withStyle(
                SpanStyle(
                    color = darkTheme.judge(
                        Color.White.copy(alpha = 0.9f),
                        Color.Black.copy(alpha = 0.87f),
                    ),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Normal,
                    baselineShift = BaselineShift(0.1f)
                ),
            ) {
                append(stringResource(R.string.analyzing_album_pictures))
            }
            withStyle(
                SpanStyle(
                    color = darkTheme.judge(
                        Color(0xFF4882FF),
                        Color(0xFF155BF0),
                    ),
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Medium,
                ),
            ) {
                append(" ${(progress * 100).toInt()}%")
            }
        },
        lineHeight = 24.sp,
        textAlign = TextAlign.Center,
    )

    Box(
        modifier = Modifier
            .padding(top = 8.dp)
            .fillMaxWidth()
            .wrapContentHeight(),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = stringResource(R.string.album_analysis_prompt),
            color = darkTheme.judge(
                Color.White.copy(alpha = 0.5f),
                Color.Black.copy(alpha = 0.5f),
            ),
            fontSize = 14.sp,
            lineHeight = 20.sp,
            fontWeight = FontWeight.Normal
        )
    }
}

@Composable
private fun AlbumAnalysisFailedPanel(
    progress: Float,
    darkTheme: Boolean = isSystemInDarkTheme(),
    tryToAnalysisAlbum: () -> Unit
) {
    TclLinearProgressIndicator(
        modifier = Modifier
            .padding(vertical = 9.dp)
            .height(6.dp)
            .fillMaxWidth(),
        progress = { progress }
    )
    AnalysisFailedWithTryAgain(
        modifier = Modifier
            .padding(top = 4.dp)
            .fillMaxWidth(),
    ) {
        tryToAnalysisAlbum.invoke()
    }
}

@Composable
fun AnalysisFailedWithTryAgain(
    modifier: Modifier = Modifier,
    darkTheme: Boolean = isSystemInDarkTheme(),
    onTryAgainClick: () -> Unit
) {
    val context = LocalContext.current
    // 1️⃣ 富文本混排
    val annotatedString = remember {
        buildAnnotatedString {
            withStyle(style = SpanStyle(
                color = darkTheme.judge(
                    Color.White.copy(alpha = 0.5f),
                    Color.Black.copy(alpha = 0.5f),
                ),
                fontSize = 14.sp,
                fontWeight = FontWeight.Normal
            )) {
                append("${context.getText(R.string.analysis_failed_new)}  ")
            }
            val start = this.length
            withStyle(
                style = SpanStyle(
                    color = darkTheme.judge(
                        Color(0xFF4882FF),
                        Color(0xFF155BF0),
                    ),
                    fontSize = 14.sp,
                    fontWeight = FontWeight(500)
                )
            ) {
                append(context.getText(R.string.try_again))
            }
            val end = this.length
            addStringAnnotation(
                tag = "TRY_AGAIN",
                annotation = "try_again",
                start = start,
                end = end
            )
        }
    }

    // 2️⃣ 检测点击位置
    var textLayoutResult by remember { mutableStateOf<TextLayoutResult?>(null) }

    BasicText(
        text = annotatedString,
        modifier = modifier
            .pointerInput(Unit) {
                detectTapGestures { offset: Offset ->
                    textLayoutResult?.let { layoutResult ->
                        val position = layoutResult.getOffsetForPosition(offset)
                        val annotations = annotatedString.getStringAnnotations(
                            tag = "TRY_AGAIN",
                            start = position,
                            end = position
                        )
                        if (annotations.isNotEmpty()) {
                            onTryAgainClick()
                        }
                    }
                }
            },
        onTextLayout = { result -> textLayoutResult = result },
        style = TextStyle.Default.copy(textAlign = TextAlign.Center)
    )
}

@Composable
private fun ColumnScope.PermissionDeniedView() {
    val context = LocalContext.current
    CommonPanel(
        painterResId = R.drawable.icon_permission_denied,
        contentResId = R.string.enable_image_permission_prompt,
        showGalleryBtn = false,
        buttonContentResId = R.string.go_to_authorize,
        buttonClick = {
            launchAppDetailsScreen(
                context = context,
                packageName = context.packageName
            )
        }
    )
}

@Composable
private fun ColumnScope.NotEnabledView(
    buttonClick: () -> Unit
) {
    CommonPanel(
        painterResId = R.drawable.icon_not_enabled,
        contentResId = R.string.enable_inspiration_suggestions_prompt,
        buttonContentResId = R.string.enable,
        buttonClick = buttonClick
    )
}

@Composable
private fun ColumnScope.SystemAlbumEmptyView() {
    CommonPanel(
        painterResId = R.drawable.icon_empty_album,
        contentResId = R.string.empty_album_desc
    )
}

@Composable
private fun ColumnScope.AlbumAnalysisFailedView(
    buttonClick: () -> Unit,
) {
    CommonPanel(
        painterResId = R.drawable.icon_album_analysis_failed,
        contentResId = R.string.album_analysis_failed_prompt,
        buttonContentResId = R.string.try_again,
        buttonClick = buttonClick,
    )
}

@Composable
private fun ColumnScope.CommonPanel(
    painterResId: Int,
    contentResId: Int,
    showGalleryBtn: Boolean = true,
    darkTheme: Boolean = isSystemInDarkTheme(),
    buttonContentResId: Int? = null,
    buttonClick: (() -> Unit?)? = null,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .weight(1F)
            .navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (showGalleryBtn) {
            Spacer(modifier = Modifier.weight(88f))
        } else {
            Spacer(modifier = Modifier.weight(156f))
        }

        Image(
            painter = painterResource(painterResId),
            contentDescription = null,
            modifier = Modifier.size(120.dp)
        )
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp),
            text = stringResource(contentResId),
            textAlign = TextAlign.Center,
            color = darkTheme.judge(
                Color.White.copy(alpha = 0.5f),
                Color.Black.copy(alpha = 0.5f),
            ),
            fontSize = 14.sp,
            lineHeight = 16.sp,
            fontWeight = FontWeight.Normal
        )
        if (buttonContentResId != null) {
            Spacer(Modifier.weight(48f))

            Row(
                Modifier
                    .background(
                        color = darkTheme.judge(
                            Color(0x144882FF),
                            Color(0x14155BF0),
                        ),
                        RoundedCornerShape(30.dp)
                    )
                    .padding(horizontal = 20.dp, vertical = 10.dp)
                    .clickable {
                        buttonClick?.invoke()
                    },
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = stringResource(buttonContentResId),
                    textAlign = TextAlign.Center,
                    color = darkTheme.judge(
                        Color(0xFF4882FF),
                        Color(0xFF155BF0),
                    ),
                    fontSize = 14.sp,
                    lineHeight = 18.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        } else {
            Spacer(Modifier.weight(86f))
        }
        Spacer(Modifier.weight(196f))
    }
}

@Composable
private fun rememberPickImageHelper(
    onSelectImages: (uris: List<@JvmSuppressWildcards Uri>) -> Unit
): (action: () -> Unit) -> Unit {
    val pickMultipleMedia = rememberLauncherForActivityResult(
        ActivityResultContracts.PickMultipleVisualMedia()
    ) { uris: List<@JvmSuppressWildcards Uri> ->
        // 返回结果是 List<Uri>，即用户选中的所有图片的 Uri
        if (uris.isNotEmpty()) {
            // 遍历处理多张图片
            uris.forEach { uri ->
                Logger.d("多图", "选中 Uri: $uri")
            }
            onSelectImages(uris)
        } else {
            Logger.d("多图", "用户没有选择任何图片")
        }
    }
    return {
        pickMultipleMedia.launch(
            PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly, maxItems = 9)
        )
    }
}