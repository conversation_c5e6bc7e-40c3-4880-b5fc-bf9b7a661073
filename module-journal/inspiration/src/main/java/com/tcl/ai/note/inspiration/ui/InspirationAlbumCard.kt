package com.tcl.ai.note.inspiration.ui

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil3.compose.AsyncImage
import coil3.compose.rememberConstraintsSizeResolver
import coil3.request.CachePolicy
import coil3.request.ImageRequest
import coil3.request.allowHardware
import coil3.request.crossfade
import com.tcl.ai.note.database.entity.Album
import com.tcl.ai.note.inspiration.viewmodel.AddPageViewModel
import com.tcl.ai.note.utils.judge
import com.tct.theme.core.designsystem.theme.TclTheme
import java.io.File
import kotlin.math.min

@Composable
fun SingleInspirationAlbumCard(album: Album, modifier: Modifier) {
    val viewModel = viewModel<AddPageViewModel>()
    val images = viewModel.albumMap[album]
    val path = images!!.first().path
    InspirationAlbumCard(album = album, modifier = modifier) {
        ImageLoader(
            modifier = Modifier
                .padding(12.dp)
                .fillMaxWidth()
                .height(150.dp),
            path = path
        )
    }
}

@Composable
fun TwoInspirationAlbumsCard(album: Album, modifier: Modifier) {
    val viewModel = viewModel<AddPageViewModel>()
    val images = viewModel.albumMap[album]
    val pathList = images!!.take(2).map { it.path }
    InspirationAlbumCard(album = album, modifier = modifier) {
        Row(
            modifier = Modifier
                .padding(12.dp)
                .fillMaxWidth()
                .height(150.dp),
            horizontalArrangement = Arrangement.Start
        ) {
            ImageLoader(
                modifier = Modifier
                    .padding(end = 12.dp)
                    .fillMaxHeight()
                    .weight(1F),
                path = pathList[0]
            )
            ImageLoader(
                modifier = Modifier
                    .fillMaxHeight()
                    .weight(1F),
                path = pathList[1]
            )
        }
    }
}

@Composable
fun ThreeInspirationAlbumsCard(album: Album, modifier: Modifier) {
    val viewModel = viewModel<AddPageViewModel>()
    val images = viewModel.albumMap[album]
    val pathList = images!!.take(3).map { it.path }
    InspirationAlbumCard(album = album, modifier = modifier) {
        Row(
            modifier = Modifier
                .padding(12.dp)
                .fillMaxWidth()
                .height(150.dp)
        ) {
            ImageLoader(
                modifier = Modifier
                    .padding(end = 12.dp)
                    .fillMaxHeight()
                    .weight(1F),
                path = pathList[0]
            )

            Column(
                modifier = Modifier
                    .weight(1F)
                    .fillMaxHeight()
            ) {
                ImageLoader(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1F),
                    path = pathList[1]
                )
                Spacer(Modifier.height(12.dp))
                ImageLoader(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1F),
                    path = pathList[2]
                )
            }
        }
    }
}

@Composable
fun FourInspirationAlbumsCard(album: Album, modifier: Modifier) {
    val viewModel = viewModel<AddPageViewModel>()
    val images = viewModel.albumMap[album]
    val pathList = images!!.take(4).map { it.path }
    InspirationAlbumCard(album = album, modifier = modifier) {
        Row(
            modifier = Modifier
                .padding(12.dp)
                .fillMaxWidth()
                .height(150.dp)
        ) {
            ImageLoader(
                modifier = Modifier
                    .padding(end = 12.dp)
                    .fillMaxHeight()
                    .weight(1F),
                path = pathList[0]
            )

            Column(
                modifier = Modifier
                    .weight(1F)
                    .fillMaxHeight()
            ) {
                ImageLoader(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1F),
                    path = pathList[1]
                )
                Spacer(Modifier.height(12.dp))
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1F)
                ) {
                    ImageLoader(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F),
                        path = pathList[2]
                    )
                    Spacer(Modifier.width(12.dp))
                    ImageLoader(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F),
                        path = pathList[3]
                    )
                }
            }
        }
    }
}

@Composable
fun FiveInspirationAlbumsCard(album: Album, modifier: Modifier) {
    val viewModel = viewModel<AddPageViewModel>()
    val images = viewModel.albumMap[album]
    val pathList = images!!.take(5).map { it.path }
    InspirationAlbumCard(album = album, modifier = modifier) {
        Row(
            modifier = Modifier
                .padding(12.dp)
                .fillMaxWidth()
                .height(150.dp)
        ) {
            ImageLoader(
                modifier = Modifier
                    .padding(end = 12.dp)
                    .fillMaxHeight()
                    .weight(1F),
                path = pathList[0]
            )

            Column(
                modifier = Modifier
                    .weight(1F)
                    .fillMaxHeight()
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1F)
                ) {
                    ImageLoader(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F),
                        path = pathList[1]
                    )
                    Spacer(Modifier.width(12.dp))
                    ImageLoader(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F),
                        path = pathList[2]
                    )
                }
                Spacer(Modifier.height(12.dp))
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1F)
                ) {
                    ImageLoader(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F),
                        path = pathList[3]
                    )
                    Spacer(Modifier.width(12.dp))
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F),
                        contentAlignment = Alignment.Center
                    ) {
                        ImageLoader(
                            modifier = Modifier
                                .fillMaxSize(),
                            path = pathList[4]
                        )
                        if (images.size > 5) {
                            Spacer(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .clip(RoundedCornerShape(10.dp))
                                    .background(color = Color.Black.copy(alpha = 0.7f))
                            )
                            Text(
                                modifier = Modifier.wrapContentSize(),
                                textAlign = TextAlign.Center,
                                text = "+${min((images.size - 5), 9)}",
                                fontSize = 20.sp,
                                maxLines = 1,
                                color = Color.White
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun MoreInspirationAlbumsCard(album: Album, modifier: Modifier) {
    FiveInspirationAlbumsCard(album, modifier)
}

@SuppressLint("DesignSystem")
@Composable
private fun InspirationAlbumCard(
    album: Album,
    modifier: Modifier,
    darkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable ColumnScope.() -> Unit
) {
    Column(
        modifier = Modifier
            .padding(bottom = 12.dp, start = 12.dp, end = 12.dp)
            .fillMaxWidth()
            .wrapContentHeight()
            .then(modifier)
            .shadow(
                elevation = 2.dp,
                shape = RoundedCornerShape(20.dp),
                ambientColor = Color(0x1A000000),
                spotColor = Color(0x1A000000)
            )
            .background(
                color = darkTheme.judge(
                    Color.White.copy(alpha = 0.08f),
                    Color.White
                ),
                shape = RoundedCornerShape(20.dp)
            )
    ) {
        content()
        InspirationAlbumTitle(title = album.albumName.orEmpty())
    }
}

@Composable
private fun InspirationAlbumTitle(title: String) {
    Text(
        text = title,
        modifier = Modifier
            .padding(horizontal = 12.dp)
            .padding(bottom = 12.dp)
            .fillMaxWidth(),
        fontSize = 14.sp,
        lineHeight = 20.sp,
        color = TclTheme.colorScheme.tctStanderTextPrimary,
        fontWeight = FontWeight.Normal
    )
}

@Composable
private fun ImageLoader(modifier: Modifier, path: String) {
    val sizeResolver = rememberConstraintsSizeResolver()
    val imageRequest = ImageRequest.Builder(LocalContext.current)
        .data(File(path)) // 关键修改点：使用文件对象
        .memoryCachePolicy(CachePolicy.ENABLED)
        .diskCachePolicy(CachePolicy.ENABLED)
        .diskCacheKey(path) // 直接使用路径作为缓存键
        .memoryCacheKey(path)
        .size(sizeResolver)
        .allowHardware(true)
        .crossfade(true)
        .build()

    AsyncImage(
        modifier = modifier.clip(shape = RoundedCornerShape(10.dp)),
        model = imageRequest,
        contentDescription = null,
        contentScale = ContentScale.Crop,
        clipToBounds = true
    )
}