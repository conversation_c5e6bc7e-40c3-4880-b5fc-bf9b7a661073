package com.tcl.ai.note.picturetotext.ui

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationVector1D
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.draggable
import androidx.compose.foundation.gestures.rememberDraggableState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.toPx
import com.tcl.ai.note.widget.clickableNoRipple
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.min

@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun BottomSheet(
    modifier: Modifier = Modifier,
    originalHeight: Dp,
    visible: Boolean,
    cancelable: Boolean = true,
    canceledOnTouchOutside: Boolean = true,
    onDismissRequest: () -> Unit,
    backHandler: (suspend (suspend () -> Unit) -> Unit)? = null,
    showAlphaAnimation: Boolean = true,
    onDismissCallback: (suspend () -> Unit) -> Unit,
    content: @Composable () -> Unit,
) {
    var visibleState by remember { mutableStateOf(!visible) }

    LaunchedEffect(Unit) {
        visibleState = visible
    }
    val backgroundAlpha = remember {
        Animatable(if (showAlphaAnimation) 0f else 1f)
    }
    BackHandler(visibleState, cancelable, backgroundAlpha, backHandler, onDismissRequest)
    BoxWithConstraints(modifier = modifier.fillMaxSize()) {
        DialogBackgroundVisibility(
            visibleState,
            backgroundAlpha,
            canceledOnTouchOutside,
            onDismissRequest
        )

        InnerDialog(
            visible = visibleState,
            onDismissRequest = onDismissRequest,
            content = content,
            originalHeight = originalHeight,
            animationBeforeDropDown = {
                backgroundAlpha.animateTo(
                    0f, animationSpec = tween(durationMillis = 200, easing = LinearEasing)
                )
            },
            onDismissCallback = onDismissCallback
        )
    }
}

@Composable
private fun BackHandler(
    visible: Boolean,
    cancelable: Boolean,
    backgroundAlpha: Animatable<Float, AnimationVector1D>,
    backHandler: (suspend (suspend () -> Unit) -> Unit)?,
    onDismissRequest: () -> Unit
) {
    val coroutineScope = rememberCoroutineScope()
    BackHandler(enabled = visible) {
        if (cancelable) {
            coroutineScope.launch {
                var beforeDismiss: suspend () -> Unit = {
                    backgroundAlpha.animateTo(
                        0f,
                        animationSpec = tween(durationMillis = 200, easing = LinearEasing)
                    )
                }
                if (backHandler != null) {
                    backHandler(beforeDismiss)
                } else {
                    beforeDismiss.invoke()
                    //onDismissRequest()
                }
            }
        }
    }
}

@Composable
private fun DialogBackgroundVisibility(
    visible: Boolean,
    backgroundAlpha: Animatable<Float, AnimationVector1D>,
    canceledOnTouchOutside: Boolean,
    onDismissRequest: () -> Unit,
) {
    val coroutineScope = rememberCoroutineScope()
    LaunchedEffect(key1 = null) {
        coroutineScope.launch {
            delay(300)
            if (backgroundAlpha.value == 1f) {
                return@launch
            }
            backgroundAlpha.animateTo(
                1f, animationSpec = tween(durationMillis = 100, easing = LinearEasing)
            )
        }
    }
    AnimatedVisibility(
        visible = visible,
        enter = fadeIn(animationSpec = tween(durationMillis = 100, easing = LinearEasing)),
        exit = fadeOut(animationSpec = tween(durationMillis = 100, easing = LinearEasing))
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .alpha(backgroundAlpha.value)
                .background(color = Color(color = 0x4D000000))
                .clearAndSetSemantics { }
                .clickableNoRipple {
                    if (canceledOnTouchOutside) {
                        onDismissRequest()
                    }
                })
    }
}

@Composable
private fun BoxScope.InnerDialog(
    visible: Boolean,
    onDismissRequest: () -> Unit,
    content: @Composable () -> Unit,
    originalHeight: Dp,
    animationBeforeDropDown: suspend () -> Unit = {},
    onDismissCallback: (suspend () -> Unit) -> Unit
) {
    val maxHeight = originalHeight
    var innerDialogHeight = remember {
        Animatable(originalHeight.value)
    }
    var currentHeight = remember {
        mutableFloatStateOf(originalHeight.value)
    }

    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    val corner = 18.dp
    val rounderModifier = Modifier.clip(
        shape = RoundedCornerShape(
            topStart = corner, topEnd = corner
        )
    )
    val isFullScreen = remember {
        mutableStateOf(false)
    }

    suspend fun dismissDialog() {
        innerDialogHeight.animateTo(0f)
        animationBeforeDropDown()
        onDismissRequest()
    }

    onDismissCallback(::dismissDialog)

    LaunchedEffect(originalHeight) {
        innerDialogHeight.snapTo(originalHeight.value)
    }

    AnimatedVisibility(
        visible = visible,
        modifier = Modifier
            .align(alignment = Alignment.BottomCenter)
            .clickableNoRipple {}
            .height(
                innerDialogHeight.value.dp.coerceAtMost(maxHeight)
            ),

        enter = slideInVertically(
            animationSpec = tween(
                durationMillis = 400,
                easing = LinearOutSlowInEasing
            ),
            initialOffsetY = {
                it
            }),

        exit = slideOutVertically(
            animationSpec = tween(
                durationMillis = 400,
                easing = LinearOutSlowInEasing
            ),
            targetOffsetY = {
                it
            }
        )
    ) {
        Box(
            modifier = rounderModifier.background(
                brush = Brush.verticalGradient(
                    colorStops = arrayOf(
                        0.15f to Color(0xFFE4F3FF),
                        0.82f to Color(0xFFF7F6FF)
                    )
                )
            )
        ) {
            Column {
                val draggableState = rememberDraggableState(onDelta = {
                    val scale = context.resources.displayMetrics.density
                    var newHeight = innerDialogHeight.value - it * 1.2f / scale
                    newHeight = newHeight.coerceIn(0f, maxHeight.value)
                    newHeight = min(newHeight, originalHeight.toPx)
                    coroutineScope.launch {
                        innerDialogHeight.snapTo(newHeight)
                    }
                })
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .draggable(
                            state = draggableState,
                            orientation = Orientation.Vertical,
                            enabled = true,
                            onDragStarted = {},
                            onDragStopped = {
                                coroutineScope.launch {
                                    var screenMiddleThird = originalHeight.value * 0.66f
                                    val halfScreenHeight = originalHeight.value * 0.5f
                                    if (isTablet) {
                                        screenMiddleThird = maxHeight.value * 0.66f
                                    }
                                    val targetHeight = when {
                                        currentHeight.floatValue == maxHeight.value -> {
                                            when {
                                                innerDialogHeight.value > screenMiddleThird -> maxHeight.value
                                                else -> 0f
                                            }
                                        }

                                        else -> when {
                                            innerDialogHeight.value >= halfScreenHeight -> maxHeight.value
                                            else -> 0f
                                        }
                                    }
                                    currentHeight.floatValue = targetHeight
                                    innerDialogHeight.animateTo(targetHeight)
                                    if (targetHeight > originalHeight.value) {
                                        isFullScreen.value = true
                                    } else if (targetHeight == originalHeight.value) {
                                        isFullScreen.value = false
                                    } else if (targetHeight == 0f) {
                                        animationBeforeDropDown()
                                        onDismissRequest()
                                    }
                                }
                            })
                        .padding(top = 12.dp, bottom = 20.dp),
                    horizontalArrangement = Arrangement.Center
                ) {
                    HorizontalDivider(
                        thickness = 4.dp,
                        color = Color(0x1A000000),
                        modifier = Modifier
                            .clip(RoundedCornerShape(10.dp))
                            .width(48.dp)
                    )
                }
                content()
            }
        }
    }
}
