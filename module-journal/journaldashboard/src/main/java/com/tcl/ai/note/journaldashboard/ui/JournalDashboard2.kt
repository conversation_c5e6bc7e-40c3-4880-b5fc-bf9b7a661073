package com.tcl.ai.note.journaldashboard.ui

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.journaldashboard.ui.popup.ShowShareLoadingPopup
import com.tcl.ai.note.journaldashboard.vm.JournalContentViewModel
import com.tcl.ai.note.journalhome.entity.HomeNoteItemEntity
import com.tcl.ai.note.journalhome.vm.HomeCoordinator
import com.tcl.ai.note.journalhome.vm.rememberHomeCoordinator
import com.tcl.ai.note.journalhome.vm.state.HomeContentListNotesUiState
import com.tcl.ai.note.journalhome.vm.state.HomeNoteUiState
import com.tcl.ai.note.journalhome.vm.state.HomeTitleMode
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isJournalFastClick
import com.tcl.ai.note.utils.setCurCoverRect
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

/**
 * 旅行日记首页
 * */
@SuppressLint("DesignSystem", "MutableCollectionMutableState")
@Composable
fun JournalDashboard2(
    navController: NavController,
    homeNoteUiState: HomeNoteUiState,
    setBlurState: (state: Boolean) -> Unit,
    onItemLongClick: (Long) -> Unit = {},
    onItemCheckedChange: (Long, Boolean) -> Unit = { _, _ -> },
    onTitleChangeToNormal: () -> Unit = {},
    onEditJournal: (HomeNoteItemEntity) -> Unit = { _ -> },
    onMoveJournal: (HomeNoteItemEntity) -> Unit = { _ -> },
    onDeleteJournal: (HomeNoteItemEntity) -> Unit = { _ -> },
    onOpenJournalContent: () -> Unit = {}
) {
    // 列表数据
    var items by remember { mutableStateOf<List<HomeNoteItemEntity>>(emptyList()) }

    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxSize(),

            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            //列表数据加载
            when (homeNoteUiState.listNotesUiState) {
                is HomeContentListNotesUiState.Loading -> {
                    CircularProgressIndicator(modifier = Modifier.padding(16.dp))
                }

                is HomeContentListNotesUiState.Success -> {
                    //数据加载成功
                    items = homeNoteUiState.listNotesUiState.homeContentListState.notes
                    if (items.isEmpty()) {
                        //没有笔记数据
                        NoJournalScreen(homeNoteUiState.isSearchMode)
                    } else {
                        // 正常列表渲染
                       JournalListContent(
                            navController = navController,
                            //homeNoteUiState = homeNoteUiState,
                            setBlurState = setBlurState,
                            //items = items,
                            onItemLongClick = onItemLongClick,
                            onItemCheckedChange = onItemCheckedChange,
                            onEditJournal = onEditJournal,
                            onMoveJournal = onMoveJournal,
                            onDeleteJournal = onDeleteJournal,
                           onOpenJournalContent = onOpenJournalContent
                       )
                    }
                }

                is HomeContentListNotesUiState.Error -> {
                    //数据加载失败
                    NoJournalScreen(false)
                }

                HomeContentListNotesUiState.Empty -> {
                    NoJournalScreen(homeNoteUiState.isSearchMode)
                }

                HomeContentListNotesUiState.SearchMode -> {

                }
            }

        }
        // 如果 isSearching 为 true，监听返回键进行处理
        BackHandler(homeNoteUiState.isSearchMode || homeNoteUiState.isEditMode) {
            onTitleChangeToNormal()
        }
    }
}

@Composable
private fun JournalListContent(
    navController: NavController,
    //items: List<HomeNoteItemEntity>,
    //homeNoteUiState: HomeNoteUiState,
    journalContentViewModel: JournalContentViewModel = hiltViewModel(),
    setBlurState: (state: Boolean) -> Unit,
    onItemLongClick: (Long) -> Unit = {},
    onItemCheckedChange: (Long, Boolean) -> Unit = { _, _ -> },
    onEditJournal: (HomeNoteItemEntity) -> Unit = { _ -> },
    onMoveJournal: (HomeNoteItemEntity) -> Unit = { _ -> },
    onDeleteJournal: (HomeNoteItemEntity) -> Unit = { _ -> },
    onOpenJournalContent: () -> Unit = {}
) {
    val coordinator: HomeCoordinator = rememberHomeCoordinator()
    val homeNoteUiState by coordinator.homeUiState.collectAsState()
    if (homeNoteUiState.listNotesUiState !is HomeContentListNotesUiState.Success) {
        return
    }
    val lazyListState = rememberLazyListState() // 为列表布局添加滚动状态跟踪
    val lazyGridState = rememberLazyGridState() // 为列表布局添加滚动状态跟踪
    ScrollToListTop(homeNoteUiState, lazyGridState, lazyListState)
    var items by remember(homeNoteUiState) { mutableStateOf((homeNoteUiState.listNotesUiState as HomeContentListNotesUiState.Success).homeContentListState.notes) }
    when (homeNoteUiState.viewType) {
        DataStoreParam.VIEW_TYPE_LARGE_IMAGE -> {
            // 自动加载更多
            LargeImageScreen(
                items,
                lazyGridState,
                homeNoteUiState.isCreateTimeSort,
                homeNoteUiState.isEditMode,
                homeNoteUiState.isSearchMode,
                onClick = { journal ->
                    Logger.d("zcl", "onClick large image item: ${journal.id}, journalTitle: ${journal.title}")
                    items = (homeNoteUiState.listNotesUiState as HomeContentListNotesUiState.Success).homeContentListState.notes
                    val item = items.find { it.id == journal.id }
                    item?.let {
                        if (homeNoteUiState.isEditMode) {
                            onItemCheckedChange(item.id.toLong(), !item.isChecked)
                        } else {
                            onOpenJournalContent.invoke()
                            //跳转到详情页
                            if (isJournalFastClick()) return@LargeImageScreen
                            val encodedTitle = URLEncoder.encode(
                                item.title,
                                StandardCharsets.UTF_8.toString()
                            )
                            val route =
                                "$ROUTE_JOURNAL_CONTENT_NO_ANIM_SCREEN?journalId=${item.id.toLong()}&journalTitle=${encodedTitle}&coverId=${item.coverId}"
                            navController.navigate(route)
                        }
                    }
                },
                viewModel = journalContentViewModel,
                onLongClick = { journal ->
                    if (!homeNoteUiState.isEditMode) {
                        onItemLongClick(journal.id.toLong())
                    }
                },
                onEditJournalClick = { item ->
                    onEditJournal(item)
                },
                onMoveJournalClick = { item ->
                    onMoveJournal(item)
                },
                onDeleteJournalClick = { item ->
                    onDeleteJournal(item)
                }
            )
        }

        DataStoreParam.VIEW_TYPE_LIST -> {
            // 自动加载更多
            ListScreen(
                items,
                lazyListState,
                homeNoteUiState.isCreateTimeSort,
                homeNoteUiState.isEditMode,
                homeNoteUiState.isSearchMode,
                onClick = { journal ->
                    Logger.d("zcl", "onClick list item: ${journal.id}, journalTitle: ${journal.title}")
                    items = (homeNoteUiState.listNotesUiState as HomeContentListNotesUiState.Success).homeContentListState.notes
                    val item = items.find { it.id == journal.id }
                    item?.let {
                        if (homeNoteUiState.isEditMode) {
                            onItemCheckedChange(item.id.toLong(), !item.isChecked)
                        } else {
                            //跳转到详情页
                            onOpenJournalContent.invoke()
                            if (isJournalFastClick()) return@ListScreen
                            val encodedTitle = URLEncoder.encode(
                                item.title,
                                StandardCharsets.UTF_8.toString()
                            )
                            val route =
                                "$ROUTE_JOURNAL_CONTENT_NO_ANIM_SCREEN?journalId=${item.id.toLong()}&journalTitle=${encodedTitle}&coverId=${item.coverId}"
                            navController.navigate(route)
                        }
                    }
                },
                viewModel = journalContentViewModel,
                onLongClick = { journal ->
                    if (!homeNoteUiState.isEditMode) {
                        onItemLongClick(journal.id.toLong())
                    }
                },
                onEditJournalClick = { item ->
                    onEditJournal(item)
                },
                onMoveJournalClick = { item ->
                    onMoveJournal(item)
                },
                onDeleteJournalClick = { item ->
                    onDeleteJournal(item)
                }
            )
        }

        else -> {
            // 自动加载更多
            FlatViewList(
                items,
                lazyListState,
                homeNoteUiState.isCreateTimeSort,
                homeNoteUiState.isEditMode,
                homeNoteUiState.isSearchMode,
                //selectedNotes,
                onClick = { journal, boundsRect ->
                    Logger.d("zcl", "onClick grid item: journalId: ${journal.id}, journalTitle: ${journal.title}")
                    items = (homeNoteUiState.listNotesUiState as HomeContentListNotesUiState.Success).homeContentListState.notes
                    val item = items.find { journal.id == it.id }
                    item?.let {
                        if (homeNoteUiState.isEditMode) {
                            onItemCheckedChange(item.id.toLong(), !item.isChecked)
                        } else {
                            //跳转到详情页
                            onOpenJournalContent.invoke()
                            if (isJournalFastClick()) return@FlatViewList
                            if (boundsRect != null) {
                                setBlurState(true)
                                setCurCoverRect(boundsRect)
                                val encodedTitle = URLEncoder.encode(
                                    item.title,
                                    StandardCharsets.UTF_8.toString()
                                )
                                val route =
                                    "$ROUTE_JOURNAL_CONTENT_SCREEN?journalId=${item.id.toLong()}&journalTitle=${encodedTitle}&coverId=${item.coverId}"
                                navController.navigate(route)
                            }
                        }
                    }
                },
                viewModel = journalContentViewModel,
                onLongClick = { journal ->
                    if (!homeNoteUiState.isEditMode) {
                        onItemLongClick(journal.id.toLong())
                    }
                },
                onEditJournalClick = { item ->
                    onEditJournal(item)
                },
                onMoveJournalClick = { item ->
                    onMoveJournal(item)
                },
                onDeleteJournalClick = { item ->
                    onDeleteJournal(item)
                }
            )
        }
    }
    ShowShareLoadingPopup(journalContentViewModel)
}

@Composable
private fun ScrollToListTop(
    homeNoteUiState: HomeNoteUiState,
    gridState: LazyGridState,
    listState: LazyListState
) {
    // 分类ID不同 滑动到顶部
    val previousCategoryId = rememberSaveable { mutableStateOf(homeNoteUiState.selectedCategoryId) }
    // 标题模式不同 滑动到顶部
    val previousTitleMode = rememberSaveable { mutableStateOf(homeNoteUiState.titleMode) }

    // 排序类型不同 滑动到顶部
    val sortType = rememberSaveable { mutableStateOf(homeNoteUiState.isCreateTimeSort) }

    // 监听分类变化，只有前后不一样才滚动到顶部
    LaunchedEffect(homeNoteUiState.selectedCategoryId, homeNoteUiState.titleMode, homeNoteUiState.isCreateTimeSort) {
        val currentCategoryId = homeNoteUiState.selectedCategoryId
        val currentTitleMode = homeNoteUiState.titleMode
        if (previousTitleMode.value != currentTitleMode && currentTitleMode == HomeTitleMode.Normal) {
            previousTitleMode.value = currentTitleMode
            scrollToTop(homeNoteUiState, gridState, listState)
        }
        if (previousCategoryId.value != currentCategoryId) {
            previousCategoryId.value = currentCategoryId
            scrollToTop(homeNoteUiState, gridState, listState)
        }
        if (sortType.value != homeNoteUiState.isCreateTimeSort) {
            sortType.value = homeNoteUiState.isCreateTimeSort
            scrollToTop(homeNoteUiState, gridState, listState)
        }
    }
}

private fun scrollToTop(
    homeNoteUiState: HomeNoteUiState,
    gridState: LazyGridState,
    listState: LazyListState
) {
    if (homeNoteUiState.viewType == DataStoreParam.VIEW_TYPE_LARGE_IMAGE) {
        gridState.requestScrollToItem(0)
    } else {
        listState.requestScrollToItem(0)
    }
}