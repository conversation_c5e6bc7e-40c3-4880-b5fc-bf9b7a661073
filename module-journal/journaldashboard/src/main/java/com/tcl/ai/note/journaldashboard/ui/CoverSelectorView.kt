package com.tcl.ai.note.journaldashboard.ui

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.journalbase.widget.indicator.StickIndicator
import com.tcl.ai.note.resources.R
import com.tcl.ai.note.journaldashboard.utils.CoverDataList.coverList
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.deviceDensity
import com.tcl.ai.note.utils.isDensity440
import com.tcl.ai.note.utils.judge

@Composable
fun CoverSelectorView(
    modifier: Modifier = Modifier,
    coverId : Int = 0,
    onCoverSelected: (Int) -> Unit,
) {
    val everyPageCoverCount = 6
    val columnCount = 3
    val rowCount = everyPageCoverCount / columnCount
    val pages = remember { derivedStateOf { coverList.toList().chunked(everyPageCoverCount) } }
    val pageCount = pages.value.size
    var selectedCoverId by remember { mutableIntStateOf(coverId) }
    val pagerState = rememberPagerState(initialPage = 0, pageCount = { pageCount })

    val coverHorizontalPadding = remember {
        if (isDensity440) {
            when {
                GlobalContext.densityDpi < deviceDensity -> 15.dp
                GlobalContext.densityDpi == deviceDensity -> 13.dp
                else -> 8.dp
            }
        } else {
            when {
                GlobalContext.densityDpi < deviceDensity -> 13.dp
                GlobalContext.densityDpi == deviceDensity -> 13.dp
                else -> 8.dp
            }
        }
    }
    val coverVerticalPadding = remember {
        if (isDensity440) {
            when {
                GlobalContext.densityDpi < deviceDensity -> 8.dp
                GlobalContext.densityDpi == deviceDensity -> 8.dp
                else -> 4.dp
            }
        } else {
            when {
                GlobalContext.densityDpi < deviceDensity -> 8.dp
                GlobalContext.densityDpi == deviceDensity -> 8.dp
                else -> 4.dp
            }
        }
    }
    val pagerHeight = remember {
        if (isDensity440) {
            when {
                GlobalContext.densityDpi < deviceDensity -> 420.dp
                GlobalContext.densityDpi == deviceDensity -> 308.dp
                else -> 270.dp
            }
        } else {
            when {
                GlobalContext.densityDpi < deviceDensity -> 380.dp
                GlobalContext.densityDpi == deviceDensity -> 280.dp
                else -> 260.dp
            }
        }
    }
    Logger.d("zzz", "pagerHeight: $pagerHeight, deviceDensity: $deviceDensity, isDensity440: $isDensity440, densityDpi: ${GlobalContext.densityDpi}")
    val coverSize = remember {
        if (isDensity440) {
            when {
                GlobalContext.densityDpi < deviceDensity -> Pair(116.dp, 164.dp)
                GlobalContext.densityDpi == deviceDensity -> Pair(96.dp, 136.dp)
                else -> Pair(88.dp, 124.dp)
            }
        } else {
            when {
                GlobalContext.densityDpi < deviceDensity -> Pair(102.dp, 144.dp)
                GlobalContext.densityDpi == deviceDensity -> Pair(88.dp, 124.dp)
                else -> Pair(82.dp, 116.dp)
            }
        }
    }

    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        HorizontalPager(
            state = pagerState,
            modifier = Modifier
                .fillMaxWidth()
                .height(pagerHeight),
        ) { pageIndex ->
            val pageCovers = pages.value[pageIndex]
            LazyColumn(modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally,) {
                items(rowCount) { rowIndex ->
                    Logger.d("CoverSelectorView", "pageIndex: $pageIndex, rowIndex: $rowIndex, pageCovers.size: ${pageCovers.size}")
                    Row(modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f), horizontalArrangement = Arrangement.Center) {
                        val coverItemModifier = Modifier
                            .padding(horizontal = coverHorizontalPadding)
                            .padding(
                                top = (rowIndex == 0).judge(4.dp, coverVerticalPadding),
                                bottom = coverVerticalPadding
                            )
                            .width(coverSize.first)
                            .height(coverSize.second)
                        for (i in (rowIndex * columnCount) until ((rowIndex + 1) * columnCount)) {
                            if (i < pageCovers.size) {
                                CoverItem(
                                    modifier = coverItemModifier,
                                    index = i,
                                    imagePainter = painterResource(id = pageCovers[i].second.resId),
                                    isSelected = selectedCoverId == pageCovers[i].second.id,
                                    onClick = {
                                        // Handle item click
                                        onCoverSelected(pageCovers[i].second.id)
                                        selectedCoverId = pageCovers[i].second.id
                                    }
                                )
                            } else {
                                Spacer(modifier = coverItemModifier)
                            }
                        }
                    }
                }
            }
        }

        StickIndicator(totalCount = pagerState.pageCount, curIndex = pagerState.currentPage)
    }
}

@SuppressLint("DesignSystem")
@Composable
fun CoverItem(
    modifier: Modifier,
    index: Int,
    imagePainter: Painter,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = modifier
            .clickable { onClick() }
            .semantics {
                contentDescription = isSelected.judge(
                    String.format(
                        GlobalContext.instance.getString(R.string.cover_selected_description),
                        index + 1
                    ),
                    String.format(
                        GlobalContext.instance.getString(R.string.cover_unselected_description),
                        index + 1
                    ),
                )
            },
    ) {
        Image(
            painter = imagePainter,
            contentDescription = null,
            modifier = Modifier
                .fillMaxSize(),
            alignment = Alignment.Center,
            contentScale = ContentScale.Crop
        )
        if (isSelected) {
            Image(
                modifier = Modifier
                    .padding(top = 8.dp, start = isDensity440.judge(10.dp, 8.dp))
                    .size(20.dp)
                    .align(Alignment.TopStart),
                painter = painterResource(id = R.drawable.ic_check_selected),
                contentDescription = stringResource(id = com.tcl.ai.note.base.R.string.checked_status),
            )
        }
    }
}

@Preview
@Composable
fun CoverSelectorViewPreview() {
    CoverSelectorView(modifier = Modifier
        .fillMaxWidth(),1) {}
}