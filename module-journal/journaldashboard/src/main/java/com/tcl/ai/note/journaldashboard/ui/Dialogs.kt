package com.tcl.ai.note.journaldashboard.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.DialogProperties
import coil3.compose.AsyncImage
import com.tcl.ai.note.base.R
import com.tcl.ai.note.journalbase.truncateVisibleCharacters
import com.tcl.ai.note.journalbase.visibleCharactersLength
import com.tcl.ai.note.journalhome.entity.HomeNoteItemEntity
import com.tcl.ai.note.journalhome.vm.state.HomeNoteUiState
import com.tcl.ai.note.journalhome.vm.state.NoteListAction
import com.tcl.ai.note.theme.NoteTclTheme
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.utils.substringByCodePoints
import com.tct.theme.core.designsystem.component.TclDialog
import com.tct.theme.core.designsystem.component.TclTextButton
import com.tct.theme.core.designsystem.component.TclTextField

@Composable
fun InputDialog(
    title: String,
    text: String,
    error: String = "",
    placeholder: String? = null,
    isError: Boolean = false,
    maxLength: Int = Int.MAX_VALUE,
    truncateStringWhenExceedLimit: Boolean = false,
    onValueChange: (String) -> Unit,
    onConfirm: (String) -> Unit,
    onDismissRequest: () -> Unit
) {
    var input by remember { mutableStateOf(text) }
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    var limitationError by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }

    DisposableEffect(Unit) {
        onDispose {
            focusManager.clearFocus()
        }
    }

    var textFieldValue by remember {
        mutableStateOf(
            TextFieldValue(
                text = text,
                selection = TextRange(text.length) // Set the cursor to the end
            )
        )
    }

    TclDialog(
        show = true,
        onDismissRequest = {
            onDismissRequest.invoke()
        },
        properties = DialogProperties(
            dismissOnClickOutside = true,
            dismissOnBackPress = true,
            usePlatformDefaultWidth = false
        ),
        title = { Text(text = title) },
        content = {
            TclTextField(
                value = textFieldValue,
                singleLine = true,
                onValueChange = {
                    var codePointLen = it.text
                    if (truncateStringWhenExceedLimit) {
                        codePointLen = truncateVisibleCharacters(it.text, maxLength)

                    }
                    limitationError = visibleCharactersLength(codePointLen) > maxLength
                    input = codePointLen
                    textFieldValue = it.copy(
                        text = codePointLen
                    )
                    onValueChange(input)
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .focusRequester(focusRequester),
                placeholder = { placeholder?.let { if (it.isNotEmpty()) Text(text = it, color = TclTheme.tclColorScheme.tctStanderTextSecondary) } },
            )
            if ((isError && input.isNotEmpty()) || limitationError) {
                Text(
                    text = error,
                    color = Color.Red,
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        },
        actions = {
            TclTextButton(onClick = { onDismissRequest.invoke() }) { Text(text = stringResource(id = R.string.cancel)) }
            TclTextButton(
                enabled = input.trim().isNotEmpty() && !isError && !limitationError,
                onClick = { onConfirm.invoke(input) }) { Text(text = stringResource(id = R.string.dialog_positive_text_ok)) }
        },
    )
}

/**
 * 通用提示弹窗
 * @param onDismiss 当对话框被关闭时执行的操作
 * @param onOk 当用户选择ok时执行的操作
 */
@Composable
fun TipDialog(
    text: String,
    onDismiss: () -> Unit,
    onOk: () -> Unit
) {
    com.tct.theme.core.designsystem.theme.TclTheme(dynamicColor = false) {
        TclDialog(
            show = true,
            onDismissRequest = { onDismiss() },
            properties = DialogProperties(dismissOnClickOutside = true, dismissOnBackPress = true),
            content = {
                Text(
                    text = text
                )
            },
            actions = {
                TclTextButton(onClick = { onDismiss.invoke() }) { Text(stringResource(id = R.string.btn_cancel)) }
                TclTextButton(onClick = { onOk.invoke() }) { Text(stringResource(id = R.string.dialog_positive_text_ok)) }
            },
        )
    }
}

/**
 * 是否执行翻页弹窗
 * @param onCancel 当对话框被关闭时执行的操作
 * @param onYes 当用户选择yes时执行的操作
 */
@Composable
fun PageTurningDialog(
    text: String,
    onCancel: () -> Unit,
    onYes: () -> Unit
) {
    com.tct.theme.core.designsystem.theme.TclTheme(dynamicColor = false) {
        TclDialog(
            show = true,
            onDismissRequest = { onCancel() },
            properties = DialogProperties(dismissOnClickOutside = true, dismissOnBackPress = true),
            content = {
                Text(
                    text = text
                )
            },
            actions = {
                TclTextButton(onClick = { onCancel.invoke() }) { Text(stringResource(id = R.string.cancel)) }
                TclTextButton(onClick = { onYes.invoke() }) { Text(stringResource(id = com.tcl.ai.note.resources.R.string.yes)) }
            },
        )
    }
}

/**
 * 是否执行翻页弹窗
 * @param imagePath 当前的敏感图片路径
 * @param onDismissRequest 当对话框被关闭时执行的操作
 * @param onRegenerate 当用户点击“重新选择图片”按钮时执行的操作
 */
@Composable
fun SensitiveImageDialog(
    imagePath: String?,
    onDismissRequest: () -> Unit,
    onRegenerate: () -> Unit
) {
    TclDialog(
        show = true,
        onDismissRequest = { onDismissRequest() },
        properties = DialogProperties(
            dismissOnClickOutside = true,
            dismissOnBackPress = true,
            usePlatformDefaultWidth = false
        ),
        content = {
            Text(
                text = stringResource(id = com.tcl.ai.note.resources.R.string.illegal_images),
                fontWeight = FontWeight.W500,
                fontSize = 20.sp
            )
            Spacer(modifier = Modifier.height(16.dp))
            Box(
                modifier = Modifier
                    .fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                if (imagePath != null)  {
                    val model = imagePath
                    // 最外层Box是居中处理，嵌套一个Box叠成两层
                    Box(
                        modifier = Modifier.size(98.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        // 本体图片
                        AsyncImage(
                            model = imagePath,
                            contentDescription = null,
                            modifier = Modifier
                                .size(128.dp)
                                .clip(RoundedCornerShape(10.dp)),
                            contentScale = ContentScale.Crop
                        )
                        // 覆盖在本体图片正中央的三角警示标志
                        if (!imagePath.isNullOrBlank()) {
                            Image(
                                painter = painterResource(id = com.tcl.ai.note.resources.R.drawable.warning_signs),
                                contentDescription = "red_exclamation",
                                modifier = Modifier
                                    .size(24.dp)
                                    .align(Alignment.TopEnd)
                                    .offset(x = 8.dp, y = (-8).dp)
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))
            Text(
                text = stringResource(id = com.tcl.ai.note.resources.R.string.sensitive_images),
                fontSize = 14.sp,
                fontWeight = FontWeight.W400,
                color = TclTheme.colorScheme.tctStanderTextPrimary
            )
            Spacer(modifier = Modifier.height(26.dp))
        },
        actions = {
            TclTextButton(
                onClick = { onRegenerate() }
            ) {
                Text(
                    text = stringResource(id = com.tcl.ai.note.resources.R.string.reselect_the_picture),
                    fontWeight = FontWeight.W500,
                    fontSize = 16.sp,
                    color = TclTheme.tclColorScheme.tctStanderAccentPrimary
                )
            }
        }
    )
}

/**
 * 删除page弹窗
 * @param onDismiss 当对话框被关闭时执行的操作
 * @param onDelete 当用户选择删除时执行的操作
 */
@Composable
fun DeletePageDialog(
    text: String,
    onDismiss: () -> Unit,
    onDelete: () -> Unit
) {
    com.tct.theme.core.designsystem.theme.TclTheme(dynamicColor = false) {
        TclDialog(
            show = true,
            onDismissRequest = { onDismiss() },
            properties = DialogProperties(dismissOnClickOutside = true, dismissOnBackPress = true),
            content = {
                Text(
                    text = text
                )
            },
            actions = {
                TclTextButton(onClick = { onDismiss.invoke() }) { Text(stringResource(id = R.string.btn_cancel)) }
                TclTextButton(onClick = { onDelete.invoke() }) { Text(stringResource(id = R.string.menu_delete_recording)) }
            },
        )
    }
}

/**
 * 选择分享方式弹框
 * @param onDismiss 当对话框被关闭时执行的操作
 * @param onShareAsImage 当用户选择分享为图片文件时执行的操作
 * @param onShareAsPdf 当用户选择分享为PDF文件时执行的操作
 */
@Composable
fun ShowShareMethodDialog(
    onDismiss: () -> Unit,
    onShareAsImage: () -> Unit,
    onShareAsPdf: () -> Unit
) {
    TclDialog(
        show = true,
        onDismissRequest = { onDismiss() },
        properties = DialogProperties(dismissOnClickOutside = true, dismissOnBackPress = true),
        content = {
            Text(
                text = stringResource(id = com.tcl.ai.note.resources.R.string.text_choose_share_method)
            )
        },
        actions = {
            Column(modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                TclTextButton(
                    onClick = { onShareAsImage.invoke() },
                ) {
                    Text(text = stringResource(id = com.tcl.ai.note.resources.R.string.text_share_image))
                }
                TclTextButton(
                    onClick = { onShareAsPdf.invoke() },
                ) {
                    Text(text = stringResource(id = com.tcl.ai.note.resources.R.string.text_share_pdf))
                }
            }
        },
    )
}

@Composable
fun DeleteDialog(
    homeNoteUiState: HomeNoteUiState,
    onAction: (NoteListAction) -> Unit
) {
    if (homeNoteUiState.isShowDeleteDialog) {
        val selectedItemCounts = homeNoteUiState.selectedCount
        val title =
            if (selectedItemCounts == 1) stringResource(com.tcl.ai.note.resources.R.string.dialog_title_delete_one_journal) else String.format(
                stringResource(com.tcl.ai.note.resources.R.string.dialog_title_delete_multiple_journals), selectedItemCounts
            )
        DeleteDataDialog(
            text = title,
            onDismiss = { onAction(NoteListAction.OnShowDeleteDialog(false)) },
            onDelete = {
                if (homeNoteUiState.isEditMode) {
                    onAction(NoteListAction.OnDelete)
                } else {
                    onAction(NoteListAction.OnDeleteItem(homeNoteUiState.selectedItem))
                }
            }
        )
    }
}

@Composable
fun DeleteItemDialog(
    onAction: (NoteListAction) -> Unit,
    deleteItem: HomeNoteItemEntity
) {
    val title = stringResource(com.tcl.ai.note.resources.R.string.dialog_title_delete_one_journal)
    DeleteDataDialog(
        text = title,
        onDismiss = { onAction(NoteListAction.OnShowDeleteDialog(false)) },
        onDelete = { onAction(NoteListAction.OnDeleteItem(deleteItem)) }
    )
}

/**
 * 显示所选图片被删除提示对话框
 * @param onDismiss 当对话框被关闭时执行的操作
 * @param onPositiveClick 当用户选择ok时执行的操作
 */
@Composable
fun ShowImageDeletedDialog(
    onDismiss: () -> Unit,
    onPositiveClick: () -> Unit
) {
    NoteTclTheme {
        TclDialog(
            show = true,
            onDismissRequest = { onDismiss() },
            properties = DialogProperties(dismissOnClickOutside = false, dismissOnBackPress = false),
            content = {
                Text(
                    text = stringResource(id = com.tcl.ai.note.resources.R.string.selected_image_deleted_prompt)
                )
            },
            actions = {
                TclTextButton(onClick = { onPositiveClick.invoke() }) { Text( stringResource(id = com.tcl.ai.note.resources.R.string.okey)) }
            },
        )
    }
}