package com.tcl.ai.note.template.ui

import android.annotation.SuppressLint
import androidx.compose.runtime.Composable
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.material3.Icon
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.boundsInWindow
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import coil3.compose.rememberConstraintsSizeResolver
import coil3.request.CachePolicy
import coil3.request.ImageRequest
import coil3.request.allowHardware
import coil3.request.crossfade
import com.tcl.ai.note.resources.R
import com.tct.theme.core.designsystem.theme.TclTheme
import com.tcl.ai.note.utils.getNavBarHeight
import com.tcl.ai.note.utils.isDensity440
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.toPx
import com.tct.theme.core.designsystem.component.TclButton
import java.io.File

@SuppressLint("MutableCollectionMutableState")
@Composable
fun SelectPhotosScreen(
    images: List<String>,
    selectedImages: List<String> = emptyList(),
    isSelectedAllMode: Boolean,
    darkTheme: Boolean = isSystemInDarkTheme(),
    onSelectedAll: () -> Unit = {},
    imageClick: (Int, String, Rect?) -> Unit = { _, _, _ -> },
    checkBoxClick: (String) -> Unit = {},
    onSelectTemplateBtnClicked: () -> Unit = {},
) {
    val gridState = rememberLazyGridState()
    val canScroll by remember {
        derivedStateOf {
            gridState.layoutInfo.visibleItemsInfo.size < gridState.layoutInfo.totalItemsCount
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize(),
        contentAlignment = Alignment.BottomCenter
    ) {
        Column(modifier = Modifier.fillMaxSize()) {
            TopOperation(
                images = images,
                selectedImages = selectedImages,
                isSelectedAllMode = isSelectedAllMode,
                modifier = Modifier.fillMaxWidth(),
                onSelectedAll = onSelectedAll
            )

            SquareGrid(
                images = images,
                selectedImages = selectedImages,
                columns = when (images.size) {
                    1, 2 -> 1 // 1张大图/双图竖排
                    in 3..4 -> 2 // 四宫格（最多两行两列）
                    else -> 3 // 九宫格（三行三列）
                },
                gridState = gridState,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                imageClick = imageClick,
                checkBoxClick = checkBoxClick,
            )

            BottomBtn(
                text = "",
                colors = ButtonColors(
                    containerColor = Color.Transparent,
                    contentColor = Color.Transparent,
                    disabledContainerColor = Color.Transparent,
                    disabledContentColor = Color.Transparent
                ),
                onClick = { },
            )
        }

        Box(
            modifier = Modifier
                .padding(top = 56.dp)
                .fillMaxWidth()
                .height(140.dp)
                .background(
                    brush = if (canScroll) {
                        Brush.linearGradient(
                            colors = darkTheme.judge(
                                listOf(
                                    Color(0x03000000), // startColor (极浅透明)
                                    Color(0xFF000000), // centerColor
                                    Color(0xFF000000), // endColor
                                ),
                                listOf(
                                    Color(0x03FFFFFF), // startColor (极浅透明)
                                    Color(0xFFFFFFFF), // centerColor
                                    Color(0xFFF4F4FD), // endColor
                                )
                            ),
                            start = Offset(0f, 0f),
                            end = Offset(0f, (140.dp - getNavBarHeight()).toPx),
                        )
                    } else {
                        Brush.linearGradient(
                            colors = listOf(
                                Color.Transparent,
                                Color.Transparent
                            )
                        )
                    }
                ),
            contentAlignment = Alignment.BottomCenter
        ) {
            BottomBtn(
                text = stringResource(id = R.string.title_select_template),
                enabled = selectedImages.isNotEmpty(),
                onClick = { onSelectTemplateBtnClicked.invoke() },
            )
        }
    }
}

@Composable
fun TopOperation(
    images: List<String>,
    selectedImages: List<String>,
    isSelectedAllMode: Boolean,
    modifier: Modifier = Modifier,
    darkTheme: Boolean = isSystemInDarkTheme(),
    onSelectedAll: () -> Unit = {},
) {
    val context = LocalContext.current
    Row(
        modifier = modifier.padding(
            top = 8.dp,
            start = 24.dp,
            end = 16.dp,
            bottom = 8.dp
        ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            modifier = Modifier.weight(1f),
            text = String.format(
                context.getString(R.string.msg_picture_selected),
                selectedImages.size
            ),
            fontSize = 20.sp,
            lineHeight = 24.sp,
            fontWeight = FontWeight.Medium,
            overflow = TextOverflow.Ellipsis,
            color = TclTheme.colorScheme.tctStanderTextPrimary,
        )

        // 全选
        if (images.size <= 9) {
            IconButton(
                onClick = { onSelectedAll() },
            ) {
                Icon(
                    modifier = Modifier.size(24.dp),
                    painter = painterResource(
                        id = isSelectedAllMode.judge(
                            com.tcl.ai.note.base.R.drawable.ic_selected_all_checked,
                            com.tcl.ai.note.base.R.drawable.ic_selected_all
                        )
                    ),
                    contentDescription = stringResource(id = com.tcl.ai.note.base.R.string.item_top_check_all),
                    tint = isSelectedAllMode.judge(
                        darkTheme.judge(
                            Color(0xFF4882FF),
                            Color(0xFF155BF0)
                        ),
                        darkTheme.judge(
                            Color.White.copy(alpha = 0.9f),
                            Color.Black
                        )
                    )
                )
            }
        } else if (selectedImages.size == 9) {
            IconButton(onClick = { onSelectedAll() }) {
                Icon(
                    modifier = Modifier.size(24.dp),
                    painter = painterResource(
                        id = com.tcl.ai.note.base.R.drawable.ic_selected_all_checked
                    ),
                    contentDescription = stringResource(id = com.tcl.ai.note.base.R.string.item_top_check_all),
                    tint = darkTheme.judge(
                        Color(0xFF4882FF),
                        Color(0xFF155BF0)
                    )
                )
            }
        } else {
            IconButton(onClick = {  }) {
                Box(modifier = Modifier.size(24.dp))
            }
        }
    }
}

@Composable
fun SquareGrid(
    images: List<String>,
    selectedImages: List<String>,
    columns: Int,
    gridState: LazyGridState,
    modifier: Modifier = Modifier,
    imageClick: (Int, String, Rect?) -> Unit,
    checkBoxClick: (String) -> Unit,
) {
    val horizontalPadding = remember(images.size, columns) {
        if (images.size == 1) {
            40.dp
        } else if (images.size == 2) {
            76.dp
        } else if (columns == 2) {
            24.dp
        } else {
            12.dp
        }
    }
    LazyVerticalGrid(
        columns = GridCells.Fixed(columns),
        state = gridState,
        modifier = modifier
            .padding(horizontal = horizontalPadding),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp),
    ) {
        items(columns) {
            Box(modifier = Modifier.height(0.dp))
        }
        items(images.size) { index ->
            val imageUrl = images[index]
            val isSelected = selectedImages.contains(imageUrl)
            ImageItem(
                index = index,
                imageUrl = imageUrl,
                isSelected = isSelected,
                imageClick = imageClick,
                roundedCornerSize = if (images.size == 1) {
                    20
                } else if (images.size <= 4) {
                    12
                } else {
                    10
                },
                checkBoxPadding = if (images.size <= 2) {
                    14
                } else {
                    10
                },
                checkBoxClick = checkBoxClick,
                modifier = modifier
                    .fillMaxWidth()
                    .aspectRatio(1f)
            )
        }
        items(columns) {
            Box(modifier = Modifier.height(0.dp))
        }
    }
}

@Composable
fun ImageItem(
    index: Int,
    imageUrl: String,
    isSelected: Boolean,
    roundedCornerSize: Int,
    checkBoxPadding: Int,
    imageClick: (Int, String, Rect?) -> Unit,
    checkBoxClick: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val selected by rememberUpdatedState(isSelected) // always the latest
    var boundsRect by remember { mutableStateOf<Rect?>(null) }
    Box(
        modifier = modifier
            .shadow(
                elevation = 4.dp,
                shape = RoundedCornerShape(roundedCornerSize.dp)
            )
            .background(Color.White, RoundedCornerShape(roundedCornerSize.dp))
            .clickable {
                imageClick(index, imageUrl, boundsRect)
            },
        contentAlignment = Alignment.TopStart
    ) {
        AsyncImage(
            model = getImageRequest(imageUrl),
            contentDescription = null,
            modifier = Modifier
                .fillMaxSize()
                .onGloballyPositioned { coordinates ->
                    boundsRect = coordinates.boundsInWindow()
                },
            contentScale = ContentScale.Crop
        )
        Image(
            modifier = Modifier
                .padding(top = checkBoxPadding.dp, start = checkBoxPadding.dp)
                .size(20.dp)
                .clickable {
                    checkBoxClick(imageUrl)
                },
            painter = painterResource(
                id = selected.judge(
                    R.drawable.ic_check_selected_accent_color,
                    R.drawable.ic_check_unselected
                )
            ),
            contentDescription = "${index + 1}" + isSelected.judge(stringResource(id = com.tcl.ai.note.base.R.string.checked_status), stringResource(id = com.tcl.ai.note.base.R.string.unchecked_status))
        )
    }
}

@Composable
fun BottomBtn(
    text: String,
    enabled: Boolean = true,
    darkTheme: Boolean = isSystemInDarkTheme(),
    colors: ButtonColors = ButtonColors(
        containerColor = darkTheme.judge(
            Color(0xFF4882FF),
            Color(0xFF155BF0),
        ),
        contentColor = Color.White,
        disabledContainerColor = darkTheme.judge(
            Color(0xFFB8C7FA),
            Color(0x4D155BF0),
        ),
        disabledContentColor = Color(0x4DFFFFFF)
    ),
    onClick: () -> Unit,
) {
    val navigationBarHeight = getNavBarHeight()
    TclButton(
        onClick = onClick,
        enabled = enabled,
        colors = colors,
        modifier = Modifier
            .padding(start = 24.dp, bottom = navigationBarHeight + 20.dp, end = 24.dp)
            .fillMaxWidth()
            .heightIn(min = isDensity440.judge(48.dp, 44.dp)),
    ) {
        Text(
            text = text,
            fontSize = 16.sp,
            lineHeight = 24.sp,
        )
    }
}

@Composable
fun getImageRequest(path: String): ImageRequest {
    val file = File(path)
    val cacheKey = path + "_" + file.lastModified()
    val sizeResolver = rememberConstraintsSizeResolver()
    return ImageRequest.Builder(LocalContext.current)
        .data(File(path)) // 关键修改点：使用文件对象
        .memoryCachePolicy(CachePolicy.ENABLED)
        .diskCachePolicy(CachePolicy.DISABLED)
        .memoryCacheKey(cacheKey)
        .size(sizeResolver)
        .allowHardware(true)
        .crossfade(true)
        .build()
}